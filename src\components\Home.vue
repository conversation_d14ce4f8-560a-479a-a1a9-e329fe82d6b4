<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
function goToPick() {
  router.push("/pick");
}
</script>

<template>
  <div class="container home-background">
    <header class="header">
      <img alt="logo" class="logo" src="/img/logo.png" />
      <!-- <span class="title">公仔面</span> -->
    </header>
    <main class="main">
      <!-- 移除过大的gif图，代码超过100mb有限制，需要压缩 -->
      <img class="main-gif" src="/img/1.png" alt="主视觉图" />
    </main>
    <footer class="footer">
      <button class="btn btn-img rule" aria-label="游戏规则"></button>
      <button
        class="btn btn-img go"
        @click="goToPick"
        aria-label="GO按钮"
      ></button>
    </footer>

    <!-- <footer class="footer">
      <button class="btn rule">游戏规则</button>
      <button class="btn go" @click="goToPick">GO</button>
    </footer> -->
  </div>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  /* background: #fff; */
  font-family: "PingFang SC", "Helvetica Neue", Arial, "Hiragino Sans GB",
    "Microsoft YaHei", sans-serif;
  overflow: hidden;
  padding: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
.home-background {
  background: url("/img/gzaimian-bg1.png") no-repeat center center;
  background-size: cover;
  min-height: 100vh;
}
.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 4.5rem;
  margin-bottom: 0.5rem;
  padding: 0 1rem;
}
.logo {
  width: 100%;
  max-width: 180px;
  height: auto;
  display: block;
  object-fit: contain;
}
.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #222;
  letter-spacing: 0.125rem;
}
.main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 1rem;
  padding: 0 1rem;
}
.main-gif {
  width: 12rem;
  height: 12rem;
  max-width: 70vw;
  max-height: 45vh;
  object-fit: contain;
  border-radius: 1rem;
}
.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 1.25rem 1.5rem 2rem 1.5rem;
  box-sizing: border-box;
  gap: 1rem;
}
.btn {
  flex: 1;
  margin: 0 0.5rem;
  padding: 0;
  border: none;
  background: none;
  background-position: center;
  cursor: pointer;
  transition: opacity 0.2s;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.btn-img {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0;
  border: none;
  width: 100%;
  height: 60px; /* 设置基础高度 */
  max-width: 300px;
  aspect-ratio: 5 / 1; /* 设置按钮比例，根据您的图片调整 */
  /* border: 1px dashed red; */
}
.btn.rule {
  background-image: url("/img/gzaimian-button-rule.png");
}
.btn.go {
  background-image: url("/img/gzaimian-button-go.png");
}

.btn:active {
  opacity: 0.8;
  transform: scale(0.96);
}
/* 小屏幕适配 */
@media (max-width: 320px) {
  .title {
    font-size: 1.8rem;
  }
  .main-gif {
    width: 10rem;
    height: 10rem;
  }
  .btn-img {
    max-width: 260px;
    aspect-ratio: 3 / 1;
  }
}

/* 大屏幕适配 */
@media (min-width: 430px) {
  .title {
    font-size: 2.5rem;
  }
  .main-gif {
    width: 14rem;
    height: 14rem;
  }
  .btn-img {
    width: 100%; 
    max-width: 320px;
    aspect-ratio: 3 / 1;
  }
}
</style>
